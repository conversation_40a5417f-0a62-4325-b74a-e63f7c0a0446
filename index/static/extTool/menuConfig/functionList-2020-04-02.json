[{"fid": 100, "code": "0011", "parentCode": "ROOT", "name": "首页", "url": "index/welcome", "icon": "welcome", "sort": 100, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2017-07-27 15:14:54"}, {"fid": 101, "code": "001100010001", "parentCode": "0011", "name": "配送费余额", "url": "index/welcome", "icon": "", "sort": 101, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2017-07-27 15:14:54"}, {"fid": 110, "code": "0001", "parentCode": "ROOT", "name": "经营信息管理", "url": "index/datainput", "icon": "datainput", "sort": 110, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2017-07-27 15:14:54"}, {"fid": 111, "code": "00010001", "parentCode": "0001", "name": "品牌管理", "url": "index/datainput/brand", "sort": 111, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111001, "code": "000100010001", "parentCode": "00010001", "name": "停/启用品牌", "url": "index/datainput/brand", "sort": 111001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111002, "code": "000100010002", "parentCode": "00010001", "name": "品牌信息及业务设置", "url": "index/datainput/brand", "sort": 111002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111003, "code": "0001000100020001", "parentCode": "000100010002", "name": "编辑品牌信息", "url": "index/datainput/brand/:brand_id", "sort": 111003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111004, "code": "0001000100020002", "parentCode": "000100010002", "name": "停/启用品牌", "url": "index/datainput/brand/:brand_id", "sort": 111004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111005, "code": "000100010003", "parentCode": "00010001", "name": "创建品牌", "url": "index/datainput/brand", "sort": 111005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 111006, "code": "000100010004", "parentCode": "00010001", "name": "删除品牌", "url": "index/datainput/brand", "sort": 111006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 112, "code": "00010002", "parentCode": "0001", "name": "门店列表", "url": "index/datainput/store", "icon": "", "sort": 112, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112001, "code": "000100020003", "parentCode": "00010002", "name": "批量管理", "url": "index/datainput/store", "icon": "", "sort": 112001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112002, "code": "0001000200030001", "parentCode": "000100020003", "name": "批量删除门店", "url": "index/datainput/store", "icon": "", "sort": 112002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112003, "code": "000100020001", "parentCode": "00010002", "name": "创建门店", "url": "index/datainput/store", "icon": "", "sort": 112003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112004, "code": "000100020007", "parentCode": "00010002", "name": "导入/更新门店", "url": "index/datainput/store", "icon": "", "sort": 112004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112005, "code": "000100020008", "parentCode": "00010002", "name": "导出门店", "url": "index/datainput/store", "icon": "", "sort": 112005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112006, "code": "000100020009", "parentCode": "00010002", "name": "更新门店库存", "url": "index/datainput/store", "icon": "", "sort": 112006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112007, "code": "000100020004", "parentCode": "00010002", "name": "管理门店", "url": "index/datainput/store", "icon": "", "sort": 112007, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112008, "code": "000100020005", "parentCode": "00010002", "name": "餐单分类", "url": "index/datainput/store", "icon": "", "sort": 112008, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 112009, "code": "000100020006", "parentCode": "00010002", "name": "库存管理", "url": "index/datainput/store", "icon": "", "sort": 112009, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-12-15 10:29:41"}, {"fid": 113, "code": "00010003", "parentCode": "0001", "name": "餐品库", "url": "index/datainput/products", "icon": "", "sort": 113, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113001, "code": "000100030001", "parentCode": "00010003", "name": "批量管理", "url": "index/datainput/products", "icon": "", "sort": 113001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113002, "code": "000100030002", "parentCode": "00010003", "name": "创建餐品", "url": "index/datainput/products", "icon": "", "sort": 113002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113003, "code": "000100030003", "parentCode": "00010003", "name": "导入/更新餐品", "url": "index/datainput/products", "icon": "", "sort": 113003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113004, "code": "000100030004", "parentCode": "00010003", "name": "导出餐品", "url": "index/datainput/products", "icon": "", "sort": 113004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113005, "code": "000100030005", "parentCode": "00010003", "name": "特殊要求管理", "url": "index/datainput/products", "icon": "", "sort": 113005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113006, "code": "000100030006", "parentCode": "00010003", "name": "餐品上下架", "url": "index/datainput/products", "icon": "", "sort": 113006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113007, "code": "000100030007", "parentCode": "00010003", "name": "编辑餐品", "url": "index/datainput/products", "icon": "", "sort": 113007, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113008, "code": "000100030008", "parentCode": "00010003", "name": "调整餐单餐品价格", "url": "index/datainput/products", "icon": "", "sort": 113008, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 113009, "code": "000100030009", "parentCode": "00010003", "name": "停/启用餐品", "url": "index/datainput/products", "icon": "", "sort": 113009, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114, "code": "00010004", "parentCode": "0001", "name": "餐单管理", "url": "index/datainput/menu", "icon": "", "sort": 114, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114001, "code": "000100040001", "parentCode": "00010004", "name": "添加门店餐单分类", "url": "index/datainput/menu", "icon": "", "sort": 114001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114002, "code": "000100040008", "parentCode": "00010004", "name": "批量编辑餐品分类", "url": "index/datainput/menu", "icon": "", "sort": 114002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114003, "code": "000100040009", "parentCode": "00010004", "name": "导入餐单", "url": "index/datainput/menu", "icon": "", "sort": 114003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114004, "code": "000100040011", "parentCode": "00010004", "name": "导出餐单", "url": "index/datainput/menu", "icon": "", "sort": 114004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114005, "code": "000100040012", "parentCode": "00010004", "name": "餐单排序设置", "url": "index/datainput/menu", "icon": "", "sort": 114005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114006, "code": "000100040002", "parentCode": "00010004", "name": "重命名分类", "url": "index/datainput/menu", "icon": "", "sort": 114006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114007, "code": "000100040010", "parentCode": "00010004", "name": "同步餐单", "url": "index/datainput/menu", "icon": "", "sort": 114007, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114008, "code": "000100040003", "parentCode": "00010004", "name": "移出/移入门店", "url": "index/datainput/menu", "icon": "", "sort": 114008, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114009, "code": "000100040004", "parentCode": "00010004", "name": "餐单编辑", "url": "index/datainput/menu", "icon": "", "sort": 114009, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114010, "code": "000100040005", "parentCode": "00010004", "name": "添加餐单", "url": "index/datainput/menu", "icon": "", "sort": 114010, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114011, "code": "000100040006", "parentCode": "00010004", "name": "删除分类", "url": "index/datainput/menu", "icon": "", "sort": 114011, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 114012, "code": "000100040007", "parentCode": "00010004", "name": "更多", "url": "index/datainput/menu", "icon": "", "sort": 114012, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115, "code": "00010005", "parentCode": "0001", "name": "BOMS设置", "url": "index/datainput/print", "icon": "", "sort": 115, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115001, "code": "000100050001", "parentCode": "00010005", "name": "打印设置Tab", "url": "index/datainput/print", "icon": "", "sort": 115001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115017, "code": "0001000500010001", "parentCode": "000100050001", "name": "添加设置", "url": "index/datainput/print", "icon": "", "sort": 115017, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115016, "code": "0001000500010002", "parentCode": "000100050001", "name": "批量管理", "url": "index/datainput/print", "icon": "", "sort": 115016, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115018, "code": "0001000500010003", "parentCode": "000100050001", "name": "编辑", "url": "index/datainput/print", "icon": "", "sort": 115018, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115003, "code": "000100050002", "parentCode": "00010005", "name": "标签设置Tab", "url": "index/datainput/print", "icon": "", "sort": 115003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115004, "code": "0001000500020001", "parentCode": "000100050002", "name": "添加标签", "url": "index/datainput/print", "icon": "", "sort": 115004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115005, "code": "0001000500020002", "parentCode": "000100050002", "name": "批量管理", "url": "index/datainput/print", "icon": "", "sort": 115005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115006, "code": "0001000500020003", "parentCode": "000100050002", "name": "编辑", "url": "index/datainput/print", "icon": "", "sort": 115006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115007, "code": "0001000500020004", "parentCode": "000100050002", "name": "设置标签状态", "url": "index/datainput/print", "icon": "", "sort": 115007, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115008, "code": "000100050003", "parentCode": "00010005", "name": "订单小票模板Tab", "url": "index/datainput/print", "icon": "", "sort": 115008, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115019, "code": "0001000500030001", "parentCode": "000100050003", "name": "添加模板", "url": "index/datainput/print", "icon": "", "sort": 115019, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115020, "code": "0001000500030002", "parentCode": "000100050003", "name": "批量管理", "url": "index/datainput/print", "icon": "", "sort": 115020, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115021, "code": "0001000500030003", "parentCode": "000100050003", "name": "设置模板状态", "url": "index/datainput/print", "icon": "", "sort": 115021, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115022, "code": "0001000500030004", "parentCode": "000100050003", "name": "编辑", "url": "index/datainput/print", "icon": "", "sort": 115022, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115009, "code": "000100050004", "parentCode": "00010005", "name": "分单小票模板Tab", "url": "index/datainput/print", "icon": "", "sort": 115009, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115010, "code": "0001000500040001", "parentCode": "000100050004", "name": "添加模板", "url": "index/datainput/print", "icon": "", "sort": 115010, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115011, "code": "0001000500040002", "parentCode": "000100050004", "name": "批量管理", "url": "index/datainput/print", "icon": "", "sort": 115011, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115012, "code": "0001000500040003", "parentCode": "000100050004", "name": "设置模板状态", "url": "index/datainput/print", "icon": "", "sort": 115012, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115013, "code": "0001000500040004", "parentCode": "000100050004", "name": "编辑", "url": "index/datainput/print", "icon": "", "sort": 115013, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115014, "code": "000100050005", "parentCode": "00010005", "name": "BOMS铃声设置Tab", "url": "index/datainput/print", "icon": "", "sort": 115014, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 115015, "code": "0001000500050001", "parentCode": "000100050005", "name": "编辑", "url": "index/datainput/print", "icon": "", "sort": 115015, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 116, "code": "00010006", "parentCode": "0001", "name": "原因管理", "url": "index/datainput/cancel", "icon": "", "sort": 116, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 120, "code": "0002", "parentCode": "ROOT", "name": "官网配置", "url": "index/website", "icon": "website", "sort": 120, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 121, "code": "00020001", "parentCode": "0002", "name": "官网配置", "url": "index/website/config", "icon": "", "sort": 121, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 121001, "code": "000200010004", "parentCode": "00020001", "name": "新增官网配置", "url": "index/website/config", "sort": 121001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 121002, "code": "000200010005", "parentCode": "00020001", "name": "批量管理", "url": "index/website/config", "sort": 121002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 121003, "code": "000200010001", "parentCode": "00020001", "name": "Web网站内容管理", "url": "index/website/config", "sort": 121003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 121004, "code": "000200010002", "parentCode": "00020001", "name": "H5网站内容管理", "url": "index/website/config", "sort": 121004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 121005, "code": "000200010003", "parentCode": "00020001", "name": "微信小程序管理", "url": "index/website/config", "sort": 121005, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 121006, "code": "000200010006", "parentCode": "00020001", "name": "编辑", "url": "index/website/config", "sort": 121006, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-06 18:07:31"}, {"fid": 122, "code": "00020002", "parentCode": "0002", "name": "白名单地址设置", "url": "index/website/whiteMap", "icon": "", "sort": 122, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 122001, "code": "000200020001", "parentCode": "00020002", "name": "新增白名单", "url": "index/website/whiteMap", "icon": "", "sort": 122001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 122002, "code": "000200020002", "parentCode": "00020002", "name": "批量管理", "url": "index/website/whiteMap", "icon": "", "sort": 122002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 122003, "code": "000200020003", "parentCode": "00020002", "name": "导入白名单", "url": "index/website/whiteMap", "icon": "", "sort": 122003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 122004, "code": "000200020004", "parentCode": "00020002", "name": "编辑", "url": "index/website/whiteMap", "icon": "", "sort": 122004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 123, "code": "00020003", "parentCode": "0002", "name": "短信设置", "url": "index/website/IM", "icon": "", "sort": 123, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 123001, "code": "000200030001", "parentCode": "00020003", "name": "添加", "url": "index/website/IM", "icon": "", "sort": 123001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 123002, "code": "000200030002", "parentCode": "00020003", "name": "短信通道申请", "url": "index/website/IM", "icon": "", "sort": 123002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 123003, "code": "000200030003", "parentCode": "00020003", "name": "短信内容管理", "url": "index/website/IM", "icon": "", "sort": 123003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 123004, "code": "000200030004", "parentCode": "00020003", "name": "删除", "url": "index/website/IM", "icon": "", "sort": 123004, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 124, "code": "00020004", "parentCode": "0002", "name": "CallCenter设置", "url": "index/website/callCenter", "icon": "", "sort": 124, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 124001, "code": "000200040001", "parentCode": "00020004", "name": "编辑", "url": "index/website/callCenter", "icon": "", "sort": 124001, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 124002, "code": "000200040002", "parentCode": "00020004", "name": "添加设置", "url": "index/website/callCenter", "icon": "", "sort": 124002, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 124003, "code": "000200040003", "parentCode": "00020004", "name": "批量删除", "url": "index/website/callCenter", "icon": "", "sort": 124003, "status": 1, "createTime": "2016-11-06 18:07:31", "updateTime": "2016-11-25 20:54:33"}, {"fid": 130, "code": "0003", "parentCode": "ROOT", "name": "顾客营销", "url": "index/marketing", "icon": "marketing", "sort": 130, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131, "code": "00030001", "parentCode": "0003", "name": "会员管理", "url": "index/marketing/member", "icon": "", "sort": 131, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131001, "code": "000300010001", "parentCode": "00030001", "name": "会员等级设置Tab", "url": "index/marketing/member", "icon": "", "sort": 131001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131002, "code": "0003000100010001", "parentCode": "000300010001", "name": "停/启用会员", "url": "index/marketing/member", "icon": "", "sort": 131002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131003, "code": "0003000100010002", "parentCode": "000300010001", "name": "新增会员等级", "url": "index/marketing/member", "icon": "", "sort": 131003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131004, "code": "0003000100010003", "parentCode": "000300010001", "name": "编辑会员等级", "url": "index/marketing/member", "icon": "", "sort": 131004, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131005, "code": "0003000100010004", "parentCode": "000300010001", "name": "删除会员等级", "url": "index/marketing/member", "icon": "", "sort": 131005, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131006, "code": "000300010002", "parentCode": "00030001", "name": "会员中心Tab", "url": "index/marketing/member", "icon": "", "sort": 131006, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131007, "code": "0003000100020001", "parentCode": "000300010002", "name": "导出会员", "url": "index/marketing/member", "icon": "", "sort": 131007, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131008, "code": "0003000100020002", "parentCode": "000300010002", "name": "会员详情", "url": "index/marketing/member", "icon": "", "sort": 131008, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 131009, "code": "0003000100020003", "parentCode": "000300010002", "name": "设置黑名单", "url": "index/marketing/member", "icon": "", "sort": 131009, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133, "code": "00030003", "parentCode": "0003", "name": "自营渠道营销管理", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133001, "code": "000300030001", "parentCode": "00030003", "name": "营销活动管理Tab", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133002, "code": "0003000300010001", "parentCode": "000300030001", "name": "新增活动", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133003, "code": "0003000300010002", "parentCode": "000300030001", "name": "批量管理", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133004, "code": "0003000300010003", "parentCode": "000300030001", "name": "停/启用", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133004, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133005, "code": "0003000300010004", "parentCode": "000300030001", "name": "修改", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133005, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133006, "code": "0003000300010005", "parentCode": "000300030001", "name": "删除", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133006, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133101, "code": "000300030002", "parentCode": "00030003", "name": "优惠劵管理Tab", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133101, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133102, "code": "0003000300020001", "parentCode": "000300030002", "name": "新增优惠劵", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133102, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133103, "code": "0003000300020002", "parentCode": "000300030002", "name": "批量管理", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133103, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133104, "code": "0003000300020003", "parentCode": "000300030002", "name": "发布", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133104, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133105, "code": "0003000300020004", "parentCode": "000300030002", "name": "修改", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133105, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 133106, "code": "0003000300020005", "parentCode": "000300030002", "name": "查看发布情况", "url": "index/marketing/selfEmployed", "icon": "", "sort": 133106, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 134, "code": "00030004", "parentCode": "0003", "name": "第三方营销管理", "url": "index/marketing/thirdMarketing", "icon": "", "sort": 134, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 135, "code": "00030005", "parentCode": "0003", "name": "自营渠道评价管理", "url": "index/marketing/selfcomment", "icon": "", "sort": 135, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136, "code": "00030006", "parentCode": "0003", "name": "积分管理", "url": "index/marketing/integral", "icon": "", "sort": 136, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136001, "code": "000300060001", "parentCode": "00030006", "name": "积分配置Tab", "url": "index/marketing/integral", "icon": "", "sort": 136001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136001001, "code": "0003000600010001", "parentCode": "000300060001", "name": "编辑", "url": "index/marketing/integral", "icon": "", "sort": 136001001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136002, "code": "000300060002", "parentCode": "00030006", "name": "获取积分Tab", "url": "index/marketing/integral", "icon": "", "sort": 136002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136002001, "code": "0003000600020001", "parentCode": "000300060002", "name": "全部规则", "url": "index/marketing/integral", "icon": "", "sort": 136002001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136002002, "code": "0003000600020002", "parentCode": "000300060002", "name": "添加", "url": "index/marketing/integral", "icon": "", "sort": 136002002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136003, "code": "000300060003", "parentCode": "00030006", "name": "消费积分Tab", "url": "index/marketing/integral", "icon": "", "sort": 136003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136003001, "code": "0003000600030001", "parentCode": "000300060003", "name": "全部规则", "url": "index/marketing/integral", "icon": "", "sort": 136003001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 136003002, "code": "0003000600030002", "parentCode": "000300060003", "name": "添加", "url": "index/marketing/integral", "icon": "", "sort": 136003002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 140, "code": "0004", "parentCode": "ROOT", "name": "第三方管理", "url": "index/thirdparty", "icon": "thirdparty", "sort": 140, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 141, "code": "00040001", "parentCode": "0004", "name": "第三方对接申请", "url": "index/thirdparty/apply", "icon": "", "sort": 141, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 141001, "code": "000400010001", "parentCode": "00040001", "name": "第三方外卖平台对接", "url": "index/thirdparty/apply", "icon": "", "sort": 141001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 141002, "code": "000400010002", "parentCode": "00040001", "name": "POS系统对接", "url": "index/thirdparty/apply", "icon": "", "sort": 141002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 141003, "code": "0004000100020001", "parentCode": "000400010002", "name": "映射码管理", "url": "index/thirdparty/apply", "icon": "", "sort": 141003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 142, "code": "00040002", "parentCode": "0004", "name": "外卖平台同步管理", "url": "index/thirdparty/syn", "icon": "", "sort": 142, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 142001, "code": "000400020001", "parentCode": "00040002", "name": "批量管理", "url": "index/thirdparty/syn", "icon": "", "sort": 142001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 142002, "code": "000400020002", "parentCode": "00040002", "name": "定时同步", "url": "index/thirdparty/syn", "icon": "", "sort": 142002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 142003, "code": "000400020003", "parentCode": "00040002", "name": "同步", "url": "index/thirdparty/syn", "icon": "", "sort": 142003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 142004, "code": "000400020004", "parentCode": "00040002", "name": "同步记录", "url": "index/thirdparty/syn", "icon": "", "sort": 142004, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 143, "code": "00040003", "parentCode": "0004", "name": "外卖平台评论管理", "url": "index/thirdparty/comment", "icon": "", "sort": 143, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 144, "code": "00040004", "parentCode": "0004", "name": "第三方配送管理", "url": "index/thirdparty/dms", "icon": "", "sort": 144, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 145, "code": "00040005", "parentCode": "0004", "name": "第三方订单索赔", "url": "index/thirdparty/compensation", "icon": "", "sort": 145, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 146, "code": "00040006", "parentCode": "0004", "name": "第三方代理配送申请", "url": "index/thirdparty/agentApply", "icon": "", "sort": 146, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 147, "code": "00040007", "parentCode": "0004", "name": "外卖平台活动管理", "url": "index/thirdparty/extPreMapping", "icon": "", "sort": 147, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 148, "code": "00040008", "parentCode": "0004", "name": "POS推送管理", "url": "index/thirdparty/posPush", "icon": "", "sort": 148, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 148001, "code": "000400080001", "parentCode": "00040008", "name": "批量管理", "url": "index/thirdparty/posPush", "icon": "", "sort": 148001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 148002, "code": "000400080002", "parentCode": "00040008", "name": "POS推送", "url": "index/thirdparty/posPush", "icon": "", "sort": 148002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 148003, "code": "000400080003", "parentCode": "00040008", "name": "重新推送", "url": "index/thirdparty/posPush", "icon": "", "sort": 148003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 150, "code": "0005", "parentCode": "ROOT", "name": "订单管理", "url": "index/order", "icon": "order", "sort": 150, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 151, "code": "00050001", "parentCode": "0005", "name": "实时订单查看", "url": "index/order/orderList", "icon": "", "sort": 151, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 151001, "code": "000500010001", "parentCode": "00050001", "name": "查看", "url": "index/order/orderList", "icon": "", "sort": 151001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 152, "code": "00050002", "parentCode": "0005", "name": "大订单管理", "url": "index/order/bigOrder", "icon": "", "sort": 152, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 154, "code": "00050003", "parentCode": "0005", "name": "外卖云店设置", "url": "index/order/cloudStore", "icon": "", "sort": 154, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 156, "code": "00050004", "parentCode": "0005", "name": "自定义报表", "url": "index/order/customReport", "icon": "", "sort": 156, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 153, "code": "00050005", "parentCode": "0005", "name": "自动拦截订单管理", "url": "index/order/interceptOrder", "icon": "", "sort": 153, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 155, "code": "00050006", "parentCode": "0005", "name": "配送订单查看", "url": "index/order/deliveryOrder", "icon": "", "sort": 155, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 156, "code": "0013", "parentCode": "ROOT", "name": "订单异常监控", "url": "index/orderMonitor", "icon": "orderMonitor", "sort": 156, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32", "hasRead": true, "hasRule": true}, {"fid": 221, "code": "00130001", "parentCode": "0013", "name": "异常监控列表", "url": "index/orderMonitor/errMonitor", "icon": "", "sort": 221, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32", "hasRead": true, "hasRule": true}, {"fid": 223, "code": "001300010001", "parentCode": "00130001", "name": "处理异常", "url": "index/orderMonitor/errMonitor", "icon": "", "sort": 223, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 222, "code": "00130002", "parentCode": "0013", "name": "异常监控配置", "url": "index/orderMonitor/config", "icon": "", "sort": 222, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 224, "code": "001300020001", "parentCode": "00130002", "name": "更改配置", "url": "index/orderMonitor/config", "icon": "", "sort": 224, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 160, "code": "0006", "parentCode": "ROOT", "name": "监控管理", "url": "index/monitor", "icon": "monitor", "sort": 160, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 161, "code": "00060001", "parentCode": "0006", "name": "平台推送失败订单", "url": "index/monitor/pushFailed", "icon": "", "sort": 161, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 163, "code": "00060002", "parentCode": "0006", "name": "超时订单监控", "url": "index/monitor/overtime", "icon": "", "sort": 163, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 164, "code": "00060003", "parentCode": "0006", "name": "配送异常订单监控", "url": "index/monitor/dmsError", "icon": "", "sort": 164, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 166, "code": "00060004", "parentCode": "0006", "name": "门店端开机监控", "url": "index/monitor/bstatus", "icon": "", "sort": 166, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 167, "code": "00060005", "parentCode": "0006", "name": "云店派单失败监控", "url": "index/monitor/cloudOrderfailed", "icon": "", "sort": 167, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 168, "code": "00060006", "parentCode": "0006", "name": "催单投诉监控", "url": "index/monitor/callOrder", "icon": "", "sort": 168, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 170, "code": "0007", "parentCode": "ROOT", "name": "系统管理", "url": "index/system", "icon": "system", "sort": 170, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171, "code": "********", "parentCode": "0007", "name": "账号管理", "url": "index/system/account", "icon": "", "sort": 171, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171001, "code": "********0001", "parentCode": "********", "name": "批量转移组织", "url": "index/system/account", "icon": "", "sort": 171001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171002, "code": "********0002", "parentCode": "********", "name": "批量管理", "url": "index/system/account", "icon": "", "sort": 171002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171003, "code": "********0003", "parentCode": "********", "name": "新增账号", "url": "index/system/account", "icon": "", "sort": 171003, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171004, "code": "********0004", "parentCode": "********", "name": "更新账号信息", "url": "index/system/account", "icon": "", "sort": 171004, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171005, "code": "********0005", "parentCode": "********", "name": "重置密码", "url": "index/system/account", "icon": "", "sort": 171005, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 171006, "code": "********0006", "parentCode": "********", "name": "修改账号", "url": "index/system/account", "icon": "", "sort": 171006, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 172, "code": "********", "parentCode": "0007", "name": "角色管理", "url": "index/system/role", "icon": "", "sort": 172, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 174, "code": "********", "parentCode": "0007", "name": "个人中心", "url": "index/system/userCenter", "icon": "", "sort": 174, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 174001, "code": "********0001", "parentCode": "********", "name": "变更密码", "url": "index/system/userCenter", "icon": "", "sort": 174001, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 174002, "code": "********0002", "parentCode": "********", "name": "更多变更", "url": "index/system/userCenter", "icon": "", "sort": 174002, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 175, "code": "00070005", "parentCode": "0007", "name": "系统日志", "url": "index/system/log", "icon": "", "sort": 175, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 180, "code": "0008", "parentCode": "ROOT", "name": "财务管理", "url": "index/finance", "icon": "finance", "sort": 180, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 181, "code": "00080001", "parentCode": "0008", "name": "取消/退款订单审核管理", "url": "index/finance/thirdRefund", "icon": "", "sort": 181, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 182, "code": "00080002", "parentCode": "0008", "name": "自营平台退款记录管理", "url": "index/finance/selfRefund", "icon": "", "sort": 182, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 183, "code": "00080003", "parentCode": "0008", "name": "平台对账管理", "url": "index/finance/reconciliation", "icon": "", "sort": 183, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 184, "code": "00080004", "parentCode": "0008", "name": "账户交易管理", "url": "index/finance/advanceFee", "icon": "", "sort": 184, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 185, "code": "00080005", "parentCode": "0008", "name": "系统服务账单", "url": "index/finance/sysBill", "icon": "", "sort": 185, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 190, "code": "0009", "parentCode": "ROOT", "name": "报表统计", "url": "index/report", "icon": "report", "sort": 190, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 191, "code": "00090001", "parentCode": "0009", "name": "运营明细表", "url": "index/report/service", "sort": 191, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 192, "code": "00090003", "parentCode": "0009", "name": "用户分析表", "url": "index/report/customer", "icon": "", "sort": 192, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 193, "code": "00090002", "parentCode": "0009", "name": "销售分析表", "url": "index/report/sale", "icon": "", "sort": 193, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 194, "code": "00090004", "parentCode": "0009", "name": "财务报表", "url": "index/report/finance", "icon": "", "sort": 194, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 200, "code": "00090005", "parentCode": "0009", "name": "运营分析图", "url": "index/report/serviceChart", "icon": "", "sort": 200, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 210, "code": "00090007", "parentCode": "0009", "name": "维度报表", "url": "index/report/weidChart", "icon": "", "sort": 210, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 310, "code": "0012", "parentCode": "ROOT", "name": "标签管理", "url": "index/label", "icon": "label", "sort": 310, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 311, "code": "00120001", "parentCode": "0012", "name": "标签管理", "url": "index/label/labelManagement", "icon": "", "sort": 311, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 312, "code": "00120002", "parentCode": "0012", "name": "标签应用", "url": "index/label/labelApplication", "icon": "", "sort": 312, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 410, "code": "0010", "parentCode": "ROOT", "name": "BI系统", "url": "index/sysBi", "icon": "bigData", "sort": 410, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}, {"fid": 411, "code": "00100001", "parentCode": "0010", "name": "BI分析", "url": "index/sysBi/bigData", "icon": "", "sort": 411, "status": 1, "createTime": "2016-11-06 18:07:32", "updateTime": "2016-11-06 18:07:32"}]