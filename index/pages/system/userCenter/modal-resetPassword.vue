<template>
  <cd-modal
    :show="visible"
    @close="$doClose"
    :noCancel="!done"
    s
    :isNeedCloseBtn="!done"
    :title="title"
    @callback="$doSave('ruleForm')"
    :btnOkDisabled="!formOk"
    noPadding>
    <div slot="modal-body">
      <div class="resetPassWordBox">
        <form autocomplete="off">
          <cd-form ref="ruleForm">
            <p
              v-if="titleText"
              class="mb10">
              {{ titleText }}
            </p>
            <cd-form-item
              :label="$lang(`旧密码`)"
              class="qr">
              <cd-input
                class="resetPassWord-input"
                v-if="iconShow.odlIcon"
                type="password"
                auto-complete="new-password"
                :placeholder="$lang(`请输入旧密码`)"
                v-model="ruleForm.oldAccountPwd"
                :width="230"
                @keyup.native="oldAccountPwdCheck"
                @blur="oldAccountPwdCheck"
                @change="oldAccountPwdCheck"
                :isErrorText="isErrorText_Old"
                :errorText="errorText_Old"></cd-input>
              <cd-input
                class="resetPassWord-input"
                v-if="!iconShow.odlIcon"
                type="text"
                :placeholder="$lang(`请输入旧密码`)"
                v-model="ruleForm.oldAccountPwd"
                :width="230"
                @keyup.native="oldAccountPwdCheck"
                @blur="oldAccountPwdCheck"
                @change="oldAccountPwdCheck"
                :isErrorText="isErrorText_Old"
                :errorText="errorText_Old"></cd-input>
              <cd-icon
                v-if="iconShow.odlIcon"
                type="show"
                :width="17"
                :height="10"
                @click="oldPassWord(ruleForm.oldAccountPwd)"
                class="iconBox"></cd-icon>
              <cd-icon
                v-else
                type="hide"
                :width="16"
                :height="6"
                @click="oldPassWord(ruleForm.oldAccountPwd)"
                class="iconBox"></cd-icon>
              <span
                solt="tip"
                class="co-tip pt5 pl5">
                {{ $lang(`*必填`) }}
              </span>
            </cd-form-item>
            <cd-form-item
              :label="$lang(`新密码`)"
              class="qr"
              style="margin-top: 20px">
              <cd-input
                class="resetPassWord-input"
                v-if="!iconShow.newIcon"
                type="text"
                :placeholder="$lang(`请输入新密码`)"
                :width="230"
                v-model="ruleForm.newAccountPwd"
                @keyup.native="newAccountPwdCheck"
                @blur="newAccountPwdCheck"
                @change="newAccountPwdCheck"
                :isErrorText="isErrorText_New"
                :errorText="errorText_New"></cd-input>
              <cd-input
                class="resetPassWord-input"
                v-if="iconShow.newIcon"
                type="password"
                auto-complete="new-password"
                :placeholder="$lang(`请输入新密码`)"
                :width="230"
                v-model="ruleForm.newAccountPwd"
                @keyup.native="newAccountPwdCheck"
                @blur="newAccountPwdCheck"
                @change="newAccountPwdCheck"
                :isErrorText="isErrorText_New"
                :errorText="errorText_New"></cd-input>
              <cd-icon
                v-if="iconShow.newIcon"
                type="show"
                :width="17"
                :height="10"
                @click="newPassWord(ruleForm.newAccountPwd)"
                class="iconBox"></cd-icon>
              <cd-icon
                v-else
                type="hide"
                :width="16"
                :height="6"
                @click="newPassWord(ruleForm.newAccountPwd)"
                class="iconBox"></cd-icon>
              <span
                solt="tip"
                class="co-tip pt5 pl5">
                {{ $lang(`*必填，8-16位，必须包含大小写字母和数字`) }}
              </span>
            </cd-form-item>
          </cd-form>
        </form>
      </div>
    </div>
  </cd-modal>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
export default {
  name: "resetPassword",
  props: {
    show: {
      type: Boolean,
      default: false
    },
    done: {
      type: Boolean,
      default: false
    },
    title: {
      type: String,
      default: i18n.tt("变更密码")
    },
    titleText: String
  },
  data() {
    return {
      visible: true // 控制 变更密码 弹窗
      ,
      formOk: false,
      ruleForm: {
        aid: Com.user.data.aid,
        oldAccountPwd: '',
        newAccountPwd: ''
      },
      rules: {},
      iconShow: {
        // 控制 icon 切换
        odlIcon: true,
        newIcon: true
      },
      isErrorText_Old: false,
      errorText_Old: i18n.tt("输入不能为空"),
      isErrorText_New: false,
      errorText_New: i18n.tt("输入不能为空"),
      isErrorArr: [] // 错误提示的组合
    };
  },
  methods: {
    oldPassWord() {
      this.iconShow.odlIcon = !this.iconShow.odlIcon;
    },
    newPassWord() {
      this.iconShow.newIcon = !this.iconShow.newIcon;
    },
    validate() {
      this.isErrorArr = [this.isErrorText_Old, this.isErrorText_New];
      if (this.ruleForm.newAccountPwd == '' || this.ruleForm.oldAccountPwd == '' || this.isErrorArr.indexOf(true) > -1) {
        // 含有ture
        this.formOk = false; // 不可点击
      } else {
        this.formOk = true; // 可点击
      }
    },
    oldAccountPwdCheck() {
      let data = this.ruleForm.oldAccountPwd;
      if (data === '') {
        this.isErrorText_Old = true;
        this.isErrorText_New = false;
      } else if (data === this.ruleForm.newAccountPwd) {
        this.isErrorText_Old = false;
        this.isErrorText_New = true;
        this.errorText_New = i18n.tt("新旧密码一致");
      } else {
        this.isErrorText_Old = false;
        this.isErrorText_New = false;
      }
      this.validate(data);
    },
    newAccountPwdCheck() {
      let data = this.ruleForm.newAccountPwd;
      if (data === '') {
        this.isErrorText_New = true;
        this.errorText_New = i18n.tt("输入不能为空");
      } else if (this.ruleForm.oldAccountPwd === data) {
        this.isErrorText_New = true;
        this.errorText_New = i18n.tt("新旧密码一致");
      } else {
        this.isErrorText_New = false;
      }
      this.validate(data);
    },
    $doClose() {
      // 关闭弹窗
      this.visible = false;
      setTimeout(() => {
        this.$emit('doClose');
      });
    },
    $doSave(formName) {
      // 保存弹窗
      if (this.formOk) {
        Com.vm.$service.get('updatePwd', this.ruleForm).then(result => {
          if (!result.$.checkResult(result, true)) {
            return;
          }
          if (result.msg == "旧密码错误") {
            Modal.alert({
              content: result.msg
            });
          } else {
            this.visible = false;
            setTimeout(() => {
              this.$emit('doSave');
            });
          }
        });
      } else {
        console.log('error submit!!');
        return false;
      }
      return;
    }
  }
}
</script>
<style scoped>
            
.iconBox {
  position: absolute;
  left: 200px;
  cursor: pointer;
}

.resetPassWordBox {
  padding-left: 20px;
  padding-top: 20px;
}

</style>

<style >
            
.resetPassWordBox .resetPassWord-input .cd-input-box .cd-input {
  padding-right: 37px !important;
}

.resetPassWordBox .resetPassWord-input .cd-input-box .errorText {
  left: 0px;
}

</style>
