<template>
  <div class="component-demo">
    <vAnchor
      ref="anchorRef"
      :data="anchorArray">
      <div id="radio">
        <h1 style="background-color: red; font-size: 50px">radio</h1>
        <div class="source">
          <v-radio
            v-model="radio1"
            label="1">
            {{ $lang(`备选项`) }}
          </v-radio>
          <v-radio
            v-model="radio1"
            label="2">
            {{ $lang(`备选项`) }}
          </v-radio>
        </div>
        <div class="source">
          <v-radio
            disabled
            v-model="radio2"
            :label="$lang(`禁用`)"
            line>
            {{ $lang(`备选项`) }}
          </v-radio>
          <v-radio
            disabled
            v-model="radio2"
            :label="$lang(`选中且禁用`)"
            line>
            {{ $lang(`备选项`) }}
          </v-radio>
        </div>
        <div class="source">
          <v-radio-group
            v-model="radio3"
            line>
            <v-radio :label="3">{{ $lang(`备选项`) }}</v-radio>
            <v-radio :label="6">{{ $lang(`备选项`) }}</v-radio>
            <v-radio :label="9">{{ $lang(`备选项`) }}</v-radio>
          </v-radio-group>
        </div>
        <div class="source">
          <v-radio-group
            v-model="radio3"
            :disabled="true">
            <v-radio :label="2">{{ $lang(`备选项`) }}</v-radio>
            <v-radio :label="4">{{ $lang(`备选项`) }}</v-radio>
            <v-radio :label="7">{{ $lang(`备选项`) }}</v-radio>
          </v-radio-group>
        </div>
      </div>
      <div class="popover popover-top">
        {{ $lang(`注意：品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置`) }}
        <div class="popover-arrow"></div>
      </div>
      <div class="popover popover-bottom">
        {{ $lang(`注意：品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置`) }}
        <div class="popover-arrow"></div>
      </div>
      <div class="popover popover-left">
        {{ $lang(`注意：品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置`) }}
        <div class="popover-arrow"></div>
      </div>
      <div class="popover popover-right">
        {{ $lang(`注意：品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置品牌设置`) }}
        <div class="popover-arrow"></div>
      </div>
      <div class="source">
        <v-button
          type="b2"
          @click="show = true">
          {{ $lang(`点击显示Modal`) }}
        </v-button>
        <v-button
          type="b3"
          @click="$showComfirm">
          {{ $lang(`点击显示Comfirm`) }}
        </v-button>
        <v-button
          type="b2"
          @click="$showAlter">
          {{ $lang(`点击显示Alert`) }}
        </v-button>
        <v-button
          type="b3"
          @click="$showMessage">
          {{ $lang(`显示Message`) }}
        </v-button>
      </div>
      <div
        id="checkbox"
        class="source">
        <h1 style="background-color: red; font-size: 50px">checkbox</h1>
        <v-checkbox-group v-model="checkVal">
          <v-checkbox
            name="'check'"
            :label="'0'"
            :disabled="true">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'1'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'2'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'3'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'4'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
        </v-checkbox-group>
      </div>
      <div class="source">
        {{ $lang(`剩一个勾选项时不可取消勾选：`) }}
        <v-checkbox-group
          v-model="checkVal1"
          :only="true"
          @change="$checkChange">
          <v-checkbox
            name="'check'"
            :label="'0'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'1'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'2'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'3'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
          <v-checkbox
            name="'check'"
            :label="'4'">
            {{ $lang(`备选项`) }}
          </v-checkbox>
        </v-checkbox-group>
      </div>
      <div
        id="form"
        class="source">
        <v-form :inline="true">
          <v-form-item prop="name">
            <v-input
              v-model="input1"
              @input="$input"
              :placeholder="$lang(`请输入内容`)"></v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`禁止输入`)"
            prop="name">
            <v-input
              v-model="input2"
              :disabled="true"></v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`日历输入框`)"
            prop="name">
            <v-input
              v-model="input3"
              date></v-input>
            <v-button
              type="b3"
              @click="$datas">
              ok
            </v-button>
          </v-form-item>
          <v-form-item
            :label="$lang(`联想下拉`)"
            prop="name">
            <v-input
              v-model="input1"
              :inputlistdata="inputlistdata"
              :placeholder="$lang(`如：水果`)">
              <template
                slot="itemText"
                scope="props">
                {{ props.item }}
              </template>
            </v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`联想下拉`)"
            prop="name">
            <v-input
              v-model="input1"
              :listKey="'label'"
              :inputlistdata="inputlistdata1"
              :placeholder="$lang(`如：水果`)">
              <template
                slot="itemText"
                scope="props">
                {{ props.item.label }}
              </template>
            </v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`联想下拉`)"
            prop="input1">
            <v-input
              v-model="input1"
              :listKey="'names'"
              :inputlistdata="inputlistdata1"
              :placeholder="$lang(`如:1`)">
              <template
                slot="itemText"
                scope="props">
                {{ props.item.names }}
              </template>
            </v-input>
          </v-form-item>
          <v-form-item
            label="input"
            prop="name">
            <v-input
              v-model="input1"
              @input="$input"
              :placeholder="$lang(`请输入内容`)"
              :chatCount="30"></v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`textarea输入框`)"
            prop="name">
            <v-input
              v-model="input2"
              type="textarea"
              :height="60"
              :width="200"></v-input>
          </v-form-item>
          <v-form-item
            :label="$lang(`textarea输入框`)"
            prop="name">
            <v-input
              v-model="input2"
              type="textarea"
              :chatCount="120"
              :height="60"></v-input>
          </v-form-item>
          <div>
            <v-form-item
              label="select"
              prop="name">
              <v-select
                :options="options"
                v-model="status"></v-select>
            </v-form-item>
          </div>
        </v-form>
      </div>
      <div class="source">
        {{ $lang(`下拉复选`) }}
        <selectCheck
          :allText="allComplete"
          @checkselect="$checkselect"
          v-model="selectVal"
          :clientTypeList="clientTypeList"></selectCheck>
      </div>
      <div class="source">
        <v-code
          :url="apk_url"
          :size="100"></v-code>
      </div>
      <div class="source">
        <v-region
          @change="$changeRegion1"
          :change-select-index="0"
          hasLabel></v-region>
      </div>
      <div class="source">
        <v-region
          @change="$changeRegion2"
          inline
          hasLabel></v-region>
      </div>
      <div class="source"><v-region @change="$changeRegion3"></v-region></div>
      <div class="source">
        <cd-country
          :initCountryId="0"
          :initProvinceId="0"
          :initCityId="0"
          @change="producerConfig"></cd-country>
      </div>
      <v-modal
        :show="show"
        @close="show = false"
        btnOkDisabled>
        <div
          slot="modal-header"
          class="modal-header">
          <h4 class="modal-title">Modal</h4>
        </div>
        <div
          slot="modal-body"
          class="modal-body">
          {{ $lang(`我是一个Modal`) }}
        </div>
      </v-modal>
      <div
        class="source"
        style="height: 50px">
        <div class="fl">
          <v-upload
            :isCrop="false"
            type="txt|xls|xlsx"
            :simplify="true"
            :text="$lang(`文件上传`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <v-upload
            :isCrop="false"
            type="image"
            :simplify="true"
            :text="$lang(`上传图片`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <v-upload
            :isCrop="true"
            type="image"
            :simplify="true"
            :text="$lang(`上传图片并剪裁`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <v-upload
            sm
            :isCrop="false"
            type="txt|xls|xlsx"
            :simplify="true"
            :text="$lang(`文件上传`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <v-upload
            sm
            :isCrop="false"
            type="image"
            :simplify="true"
            :text="$lang(`上传图片`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <v-upload
            sm
            :isCrop="true"
            type="image"
            :simplify="true"
            :text="$lang(`上传图片并剪裁`)"
            @callback="$uploaded"></v-upload>
        </div>
        <div class="fl ml10">
          <cd-continuous-upload
            :limit="10"
            type="jpg|jpeg|png"
            :beforeUpload="$beforeUpload"
            ref="continuousUpload"
            :fileList="fileList"
            :onSuccess="$onSuccess"
            :onProgress="$onProgress">
            <cd-button type="b3">{{ $lang(`点击添加图片`) }}</cd-button>
          </cd-continuous-upload>
          <cd-button
            type="b3"
            @click="$submitUpload">
            {{ $lang(`上传`) }}
          </cd-button>
        </div>
      </div>
      <div
        id="button"
        class="source">
        <div class="mt10">
          <v-button type="b1">b1</v-button>
          <v-button
            type="b1"
            round>
            b1
          </v-button>
          <v-button
            type="b1"
            plain>
            b1-plain
          </v-button>
          <v-button
            type="b1"
            text>
            b1-text
          </v-button>
          <v-button
            type="b1"
            gray>
            b1-gray
          </v-button>
          <v-button
            type="b1"
            disabled>
            b1-disabled
          </v-button>
        </div>
        <br />
        <div class="mt10">
          <v-button type="b2">b2</v-button>
          <v-button
            type="b2"
            round>
            b2
          </v-button>
          <v-button
            type="b2"
            plain>
            b2-plain
          </v-button>
          <v-button
            type="b2"
            text>
            b2-text
          </v-button>
          <v-button
            type="b2"
            gray>
            b2-gray
          </v-button>
          <v-button
            type="b2"
            disabled>
            b2-disabled
          </v-button>
        </div>
        <br />
        <div class="mt10">
          <v-button type="b3">b3</v-button>
          <v-button
            type="b3"
            round>
            b3
          </v-button>
          <v-button
            type="b3"
            plain>
            b3-plain
          </v-button>
          <v-button
            type="b3"
            text>
            b3-text
          </v-button>
          <v-button
            type="b3"
            gray>
            b3-gray
          </v-button>
          <v-button
            type="b3"
            disabled>
            b3-disabled
          </v-button>
        </div>
        <br />
        <div class="mt10">
          <v-button type="b4-1">b4-1</v-button>
          <v-button
            type="b4-1"
            round>
            b4-1
          </v-button>
          <v-button
            type="b4-1"
            plain>
            b4-1-plain
          </v-button>
          <v-button
            type="b4-1"
            text>
            b4-1-text
          </v-button>
          <v-button
            type="b4-1"
            gray>
            b4-1-gray
          </v-button>
          <v-button
            type="b4-1"
            disabled>
            b4-1-disabled
          </v-button>
        </div>
        <div class="mt10">
          <v-button type="b4-2">b4-2</v-button>
          <v-button
            type="b4-2"
            round>
            b4-2
          </v-button>
          <v-button
            type="b4-2"
            plain>
            b4-2-plain
          </v-button>
          <v-button
            type="b4-2"
            text>
            b4-2-text
          </v-button>
          <v-button
            type="b4-2"
            gray>
            b4-2-gray
          </v-button>
          <v-button
            type="b4-2"
            disabled>
            b4-2-disabled
          </v-button>
        </div>
        <br />
        <div
          id="buttonb5"
          class="mt10">
          <v-button type="b5">b5</v-button>
          <v-button
            type="b5"
            round>
            b5
          </v-button>
          <v-button
            type="b5"
            plain>
            b5-plain
          </v-button>
          <v-button
            type="b5"
            text>
            b5-text
          </v-button>
          <v-button
            type="b5"
            gray>
            b5-gray
          </v-button>
          <v-button
            type="b5"
            disabled>
            b5-disabled
          </v-button>
        </div>
        <br />
        <div class="mt10">
          <v-button type="b6">b6</v-button>
          <v-button
            type="b6"
            round>
            b6
          </v-button>
          <v-button
            type="b6"
            plain>
            b6-plain
          </v-button>
          <v-button
            type="b6"
            text>
            b6-text
          </v-button>
          <v-button
            type="b6"
            gray>
            b6-gray
          </v-button>
          <v-button
            type="b6"
            disabled>
            b6-disabled
          </v-button>
        </div>
        <div class="mt10">
          <v-button type="b7">b7</v-button>
          <v-button
            type="b7"
            round>
            b7
          </v-button>
          <v-button
            type="b7"
            plain>
            b7-plain
          </v-button>
          <v-button
            type="b7"
            text>
            b7-text
          </v-button>
          <v-button
            type="b7"
            gray>
            b7-gray
          </v-button>
          <v-button
            type="b7"
            disabled>
            b7-disabled
          </v-button>
        </div>
        <div class="mt10">
          <v-button type="b8">b8</v-button>
          <v-button
            type="b8"
            round>
            b8
          </v-button>
          <v-button
            type="b8"
            plain>
            b8-plain
          </v-button>
          <v-button
            type="b8"
            tesxt>
            b8-text
          </v-button>
          <v-button
            type="b8"
            gray>
            b8-gray
          </v-button>
          <v-button
            type="b8"
            disabled>
            b8-disabled
          </v-button>
        </div>
        <div class="mt10">
          <v-button type="b9">b9</v-button>
          <v-button
            type="b9"
            round>
            b9
          </v-button>
          <v-button
            type="b9"
            plain>
            b9-plain
          </v-button>
          <v-button
            type="b9"
            text>
            b9-text
          </v-button>
          <v-button
            type="b9"
            gray>
            b9-gray
          </v-button>
          <v-button
            type="b9"
            disabled>
            b9-disabled
          </v-button>
        </div>
        <br />
        <div class="mt10">
          <v-button type="b4-1">{{ $lang(`中式快餐类套餐`) }}</v-button>
          <v-button
            type="b4-1"
            active>
            {{ $lang(`中式快餐类套餐`) }}
          </v-button>
          <v-button type="b4-2">{{ $lang(`主食 (饭) +饮品`) }}</v-button>
          <v-button
            type="b4-2"
            active>
            {{ $lang(`主食 (饭) +饮品`) }}
          </v-button>
        </div>
        <br />
        <div id="buttonbgroup">
          <v-button-group @switch="$switch">
            <v-button
              type="b2"
              value="cancel-b2"></v-button>
            <v-button
              type="b2"
              value="center-b2"></v-button>
            <v-button
              type="b2"
              value="ok-b2"></v-button>
          </v-button-group>
        </div>
        <br />
        <div>
          <v-button-group>
            <v-button
              type="b2"
              :btnSpin="true">
              spin-b2
            </v-button>
          </v-button-group>
        </div>
      </div>
      <div class="source">
        <v-button
          type="b2"
          round>
          {{ $lang(`清除验证-b2`) }}
        </v-button>
        <v-button
          type="b2"
          plain>
          {{ $lang(`重置验证-b2`) }}
        </v-button>
        <v-button
          type="b2"
          plain
          round>
          {{ $lang(`重置验证-b2`) }}
        </v-button>
        <v-button
          type="b2"
          text>
          {{ $lang(`重置验证-b2`) }}
        </v-button>
      </div>
      <div class="source">
        <cd-switch
          disabled
          checked></cd-switch>
        <cd-switch
          :onText="$lang(`按周`)"
          :offText="$lang(`按月`)"
          :checked="switch1.value1"></cd-switch>
        <cd-switch
          :onText="$lang('显示')"
          :offText="$lang('隐藏')"
          :btnText="switch1.value2 ? $lang('隐藏品牌信息') : $lang('显示品牌信息')"
          :checked="switch1.value2"
          :btnWidth="90"
          :width="135"
          @change="$onChangeSwitch"></cd-switch>
      </div>
      <div class="source">
        <v-Date
          :startTime="startTime"
          :endTime="endTime"
          :label1="$lang(`评论时间：`)"
          :label2="$lang(`至`)"
          :day="7"
          @focus="$dataChange"></v-Date>
      </div>
      <div class="source">
        <batch-manage
          :isBatchs="isBatchs"
          @change="$doClick"
          :btnArr="batchs"></batch-manage>
      </div>
      <div class="source">
        <cd-button
          type="b3"
          @click="$selectBrand('selectBrands')">
          {{ $lang(`选择多个品牌`) }}
        </cd-button>
        <cd-button
          type="b3"
          @click="$selectBrand('selectBrand')">
          {{ $lang(`选择一个品牌`) }}
        </cd-button>
        <cd-button
          type="b3"
          @click="$selectBrand('progress')">
          {{ $lang(`进度条弹窗`) }}
        </cd-button>
        <div v-if="isModal">
          <cd-select-brands
            v-if="configModal.selectBrands"
            @callback="$doSave"
            @close="$closeModal"
            :selectedBrand="selectedBrand"></cd-select-brands>
          <cd-select-brand
            v-if="configModal.selectBrand"
            @callback="$doSave"
            @close="$closeModal"></cd-select-brand>
        </div>
      </div>
      <div class="source">
        <cd-button
          type="b3"
          @click="$selectStore('selectStore', 1)">
          {{ $lang(`可选择多个门店`) }}
        </cd-button>
        <cd-button
          type="b3"
          @click="$selectStore('selectStore', 2)">
          {{ $lang(`仅选择一个门店`) }}
        </cd-button>
        <div v-if="isModal">
          <cdSelectStores
            v-model="datas"
            v-if="configModal.selectStore && storeType == 1"
            @callback="$doSave"
            @close="$closeModal">
            <template slot="leftHeader">
              <cd-input
                v-model="datas.storeName"
                :placeholder="$lang(`门店名称`)"></cd-input>
            </template>
          </cdSelectStores>
          <cd-SelectStore
            v-model="datas"
            v-if="configModal.selectStore && storeType == 2"
            @callback="$doSave"
            @close="$closeModal">
            <template slot="leftHeader">
              <cd-input
                v-model="datas.storeName"
                :placeholder="$lang(`门店名称`)"
                class="mr10"></cd-input>
            </template>
          </cd-SelectStore>
          <cdProgress
            v-model="progressData"
            v-if="configModal.progress"
            @callback="$doSave"
            @close="$closeModal"></cdProgress>
        </div>
      </div>
      <div
        id="icon"
        class="source">
        <cd-icon type="user"></cd-icon>
        <cd-icon
          type="up2"
          switchable></cd-icon>
        <cd-icon
          type="welcome"
          :status="imgActive ? 'active' : ''"
          @click="imgActive = !imgActive"></cd-icon>
        <cd-icon
          type="prev"
          status="disabled"></cd-icon>
        <i class="iconfont icon-label"></i>
        <span
          class="din"
          style="width: 30px; height: 30px">
          <i class="iconfont icon-icon_pinpai"></i>
        </span>
      </div>
    </vAnchor>
  </div>
</template>
<script>
  import { i18n } from "project/index/libs/i18n";
import vRadio from '@/components/Radio';
import vModal from '@/components/Modal';
import vButton from '@/components/Button';
import vCheckbox from '@/components/Checkbox';
import vTabs from '@/components/Tabs';
import vMenu from '@/components/Menu';
import vIcon from '@/components/Icon';
import vInput from '@/components/Input';
import Form from '@/components/Form';
import Select from '@/components/Select';
import Page from 'project/index/components/Page';
import vUpload from '@/components/Upload';
import vRegion from 'project/index/components/Region';
import cdCountry from "project/index/components/Country";
import vCode from 'project/index/components/QrCode';
import vDate from '@/components/Date';
import batchManage from 'project/index/components/batchManage';
import cdSwitch from 'project/index/components/Switch';
import { SelectBrands, SelectBrand } from 'project/index/components/SelectBrand';
// import cdSelectBrandOne from 'project/index/components/SelectBrandOne';
import selectCheck from 'project/index/components/selectCheck';
import { SelectStores, SelectStore } from 'project/index/components/SelectStore';
import Progress from 'project/index/components/Progress';
import vAnchor from 'project/index/components/Anchor';
export default {
  name: 'componentDemo',
  data() {
    let static_url = this.$urlToolkit.getUrls({
      urlKey: 'staticUrl'
    }).qc;
    return {
      anchorArray: [{
        tar: "#radio",
        title: "radio"
      }, {
        tar: "#checkbox",
        title: "checkbox"
      }, {
        tar: "#form",
        title: "form"
      }, {
        tar: "#button",
        title: "button",
        children: [{
          tar: "#buttonb5",
          title: "buttonb5"
        }, {
          tar: "#buttonbgroup",
          title: "buttonbgroup"
        }]
      }, {
        tar: "#icon",
        title: "icon"
      }],
      apk_url: `${static_url}/5.0pos/QC_BOMS%20APP%20v5.0.1.apk`,
      show: false,
      imgActive: false,
      radio1: '1',
      radio2: i18n.tt("选中且禁用"),
      radio3: 3,
      checked: false,
      modalTest: {
        title: i18n.tt("Modal组件"),
        showModal: false,
        showComfirm: false
      },
      active: 1,
      checkVal: ['2'],
      checkVal1: [],
      menuTest: {
        activeIndex: '1'
      },
      isModal: false,
      configModal: {
        selectBrands: false,
        selectBrand: false,
        selectStore: false,
        progress: false
      },
      input1: '',
      input2: 'hello',
      input3: '',
      inputlistdata: [i18n.tt("苹果"), i18n.tt("香蕉"), i18n.tt("梨"), 1, 2, 4,, 3, 2, 5, 9, 8, 7, 6, 10],
      inputlistdata1: [{
        label: i18n.tt("苹果"),
        name2: '1',
        names: 1
      }, {
        label: i18n.tt("香蕉"),
        name2: '2',
        names: 2
      }, {
        label: i18n.tt("水果"),
        name2: '3',
        names: 3
      }, {
        label: i18n.tt("橘子"),
        name2: '4',
        names: 4
      }, {
        label: i18n.tt("葡萄"),
        name2: '5',
        names: 5
      }, {
        label: i18n.tt("脐橙"),
        name2: '6',
        names: 6
      }],
      status: 0,
      region1: {
        provinceId: 0,
        cityId: 0,
        districtId: 0
      },
      region2: {
        provinceId: 0,
        cityId: 0,
        districtId: 0
      },
      region3: {
        provinceId: 0,
        cityId: 0,
        districtId: 0
      },
      switch1: {
        value1: true,
        value2: true
      },
      options: [{
        value: '0',
        label: i18n.tt("全部")
      }, {
        value: '1',
        label: i18n.tt("启用")
      }, {
        value: '2',
        label: i18n.tt("停用")
      }, {
        value: '3',
        label: i18n.tt("全部")
      }, {
        value: '4',
        label: i18n.tt("启用")
      }, {
        value: '5',
        label: i18n.tt("停用")
      }, {
        value: '5',
        label: i18n.tt("停用")
      }, {
        value: '6',
        label: i18n.tt("停用")
      }, {
        value: '7',
        label: i18n.tt("停用")
      }, {
        value: '8',
        label: i18n.tt("停用")
      }],
      startTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') //时间开始
      ,
      endTime: Com.dateFormat(new Date(), 'yyyy-MM-dd') //时间结束
      ,
      isBatchs: false // 控制批量管理--至少选择一条数据的显示
      ,
      batchs: [{
        name: i18n.tt("批量导入白名单"),
        event: this.$importMap
      }, {
        name: i18n.tt("批量导出白名单"),
        event: this.$exportMap
      }, {
        name: i18n.tt("批量设置门店状态"),
        event: this.$setStore
      }, {
        name: i18n.tt("批量删除"),
        event: this.$delStore
      }] // 批量管理--下拉按钮
      ,
      selectedBrand: [
        // {brandId:10005488,name:'测试品牌'}
      ] //已选择品牌
      ,
      clientTypeList: [{
        value: 1,
        label: 'a'
      }, {
        value: 2,
        label: 'b'
      }, {
        value: 3,
        label: 'c'
      }, {
        value: 4,
        label: 'd'
      }, {
        value: 5,
        label: 'e'
      }],
      selectVal: [],
      allComplete: i18n.tt("全部"),
      datas: {
        storeName: ''
      },
      isSingle: false,
      storeType: 1,
      fileList: [],
      progressData: 1
    };
  },
  created() {
    // this.$refs.anchorRef.goAnchor('#buttonb5')
  },
  methods: {
    $onProgress(p, file) {
      console.log('p', p, file);
    },
    $onSuccess(ossPath, result, data) {
      console.log(ossPath, result, data);
    },
    $beforeUpload(data) {
      console.log('beforeUpload', data);
      const isJPG = data.type === 'image/jpeg' || data.type === 'image/png';
      isJPG && this.fileList.push(data);
      console.log('this.fileList', this.fileList);
    },
    $submitUpload() {
      this.$refs.continuousUpload.$submit();
    },
    $click() {
      console.log('click menu');
    },
    $showMessage() {
      Com.$messager(i18n.tt("无此权限"));
    },
    $showComfirm() {
      Modal.confirm({});
      // Com.$alert('无此权限');
    },
    $showAlter() {
      Modal.alert({
        content: i18n.tt("无此权限"),
        onOk() {
          console.log('aaa');
        }
      });
    },
    hideModal() {
      this.modalTest.showModal = false;
    },
    $switch() {
      console.log('切换按钮');
    },
    $onChangeSwitch(value) {
      this.switch1.value2 = value;
    }
    // 测试tab
    ,
    $deleteTab() {
      this.apps.splice(1, 1);
    },
    $changeTab() {
      this.active = 2;
    },
    $removeTab(index) {
      this.apps.splice(index, 1);
    },
    $addTab_last() {
      this.apps.push({
        appname: 'nexTab'
      });
      this.active = this.apps.length - 1;
    },
    $addTab_center() {
      this.apps.splice(5, 0, {
        appname: i18n.tt("添加的tab")
      });
      this.active = 5;
    },
    $onTabChange(newVal) {
      console.log('newVal', newVal);
    },
    $handleSelect() {
      console.log('select menu');
    },
    $input(value) {
      console.log(value, 'input');
    },
    $uploaded(err, path) {
      if (err) {
        vModal.alert({
          content: i18n.tt("上传失败")
        });
      } else {
        vModal.alert({
          content: i18n.tt("上传成功，地址为：{0}", [path])
        });
      }
    },
    $changeRegion1(data) {
      Com.cloneObjects(this.region1, data);
    },
    $changeRegion2(data) {
      Com.cloneObjects(this.region2, data);
    },
    $changeRegion3(data) {
      Com.cloneObjects(this.region3, data);
    },
    $changeRegion4(data) {
      console.log(data);
    },
    $datas() {
      console.log(this.input3, '9399');
    },
    $dataChange(e) {
      this.startTime = e.startTime;
      this.endTime = e.endTime;
      console.log(8585, e.startTime, e.endTime);
    },
    $doClick(index) {
      this.isBatchs = true; // 隐藏 "至少选择一条数据"
    },
    $selectBrand(name) {
      console.log(name, '1');
      this.isModal = true;
      this.configModal[name] = true;
      var test66 = setInterval(() => {
        this.progressData += 1;
        if (this.progressData > 100 || this.progressData == 100) {
          this.progressData = 100;
          clearInterval(test66);
        }
      }, 500);
    },
    $selectStore(name, type) {
      this.storeType = type;
      this.isModal = true;
      this.configModal[name] = true;
    },
    $doSave(selectBrand, name) {
      console.log(selectBrand, name, '品牌选择');
      this.isModal = false;
      this.configModal[name] = false;
    },
    $closeModal(name) {
      this.isModal = false;
      this.configModal[name] = false;
    },
    $checkChange() {
      console.log(this.checkVal1, '不可取消');
    },
    $checkselect(obj) {
      console.log(obj, 'obj');
      if (obj.value.length > 1) {
        this.allComplete = i18n.tt("部分");
      } else if (obj.value.length == 1 && obj.value[0] != 0) {
        this.allComplete = obj.value[0];
      } else {
        this.allComplete = i18n.tt("全部");
      }
      console.log(this.allComplete, '98');
    },
    producerConfig(ids, names) {
      console.log(ids, names);
    }
  },
  computed: {},
  components: {
    vAnchor,
    vRadio,
    vRadioGroup: vRadio.Group,
    vModal,
    vButton,
    vButtonGroup: vButton.Group,
    vCheckbox,
    vCheckboxGroup: vCheckbox.Group,
    vTabs,
    vTab: vTabs.Tab,
    vMenu,
    vMenuItem: vMenu.MenuItem,
    vMenuItemGroup: vMenu.MenuItemGroup,
    vSubMenu: vMenu.SubMenu,
    vIcon,
    vInput: vInput,
    vForm: Form,
    vFormItem: Form.FormItem,
    vSelect: Select,
    Page,
    vUpload,
    vRegion,
    cdCountry,
    vCode,
    vDate,
    batchManage,
    cdSwitch,
    cdSelectBrands: SelectBrands,
    cdSelectBrand: SelectBrand,
    selectCheck: selectCheck,
    cdSelectStores: SelectStores,
    cdSelectStore: SelectStore,
    cdProgress: Progress

    // ,cdSelectBrandOne:cdSelectBrand.cdSelectBrandOne
  },
  beforeRouteEnter(to, from, next) {
    next(() => {
      // console.log('loading welcome')
    });
  },
  beforeRouteLeave(to, from, next) {
    // console.log('datainput');
    next(vm => {
      // console.log('datainput-vm,leave');
    });
  }
}
</script>
<style >
            


</style>

<style scoped scoped="true">
            
    .component-demo{
        height: 100%;
        overflow-y:auto;
    }
	.source{
		padding: 15px;
    }

    /* 说明浮窗 */
    .popover{
        width:280px;
    }
    .popover-left{
        top: 80px;
        left: 200px;
    }
    .popover-right{
        top:80px;
        left:550px;
    }
    .popover-top{
        top:150px;
        left:200px;
    }
    .popover-bottom{
        top:150px;
        left:550px;
    }
    /* 修改箭头的位置 */
    .popover-top .popover-arrow{
        left:20%;
    }


</style>
