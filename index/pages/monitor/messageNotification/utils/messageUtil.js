import { i18n } from "project/index/libs/i18n";
/*
 * @Author: soji
 * @Description: 消息工具类
 * @Date: 2021-05-13 17:56:23
 */

import { NOTIFICATION_WAYS, BASE_MSG_TYPE_LIST } from "./common.js";
class MessageUtil {
  constructor() {}

  /**
   * 是否订阅消息
   * @param {消息数据} msg 
   * @returns boolean
   */
  isSubcribe(msg) {
    let result = false;
    if (msg) {
      // TODO: 完善判断逻辑
      result = true;
    }
    return result;
  }

  /**
   * 
   * @param {消息item} msgItem 
   * @param {消息下可选的通知方式列表} notificationWays 
   * @returns 请求接口的参数对象
   */
  getSubcribeNetParam(msgItem, notificationWays) {
    // emailList邮箱 phoneList手机 acceptCustomerMsg顾客接收信息  showData显示监控 useBell响铃
    let wayParam = notificationWays.map(item => {
      let netWayItem = {};
      // 传递参数的值如果有needInput,则取input字段，否则取check字段
      netWayItem[item.netField] = item.needInput ? item.input : item.check;
      return netWayItem;
    });
    return wayParam;
  }

  /**
   * 
   * @param {消息item} msgItem 
   * @returns 返回msgItem的notificationWays值
   */
  getNotificationWays(msgItem) {
    let msgTypeItem = BASE_MSG_TYPE_LIST.find(msgTypeItem => msgTypeItem.type === msgItem.type);
    return msgTypeItem ? msgTypeItem.notificationWays || [] : [];
  }
  initJustCheckWayItem(wayItem, msgItem, msgItemField) {
    wayItem.check = msgItem[msgItemField];
  }
  checkEmailInput(emailInput, nullTip = i18n.tt("请输入自定义邮箱")) {
    let pass = true,
      tips = '';
    let emails = emailInput.split(',');
    if (!emailInput) {
      pass = false;
      tips = nullTip || '';
    } else {
      emails.forEach(item => {
        if (!/^([a-zA-Z0-9]+[_|\-|.]+)*[a-zA-Z0-9]+@[a-zA-Z0-9_-]+(\.[a-zA-Z0-9_-]+)+$/.test(item)) {
          pass = false;
          tips = i18n.tt("请输入有效的邮箱");
        }
      });
    }
    return {
      pass,
      tips
    };
  }
  checkPhoneInput(phoneInput, nullTip = i18n.tt("请输入自定义手机号")) {
    let pass = true,
      tips = '';
    let phones = phoneInput.split(',');
    if (!phoneInput) {
      pass = false;
      tips = nullTip || '';
    } else {
      phones.forEach(item => {
        if (!/^[0-9\-]+$/.test(item)) {
          pass = false;
          tips = i18n.tt("请填写正确的号码格式");
        }
      });
    }
    return {
      pass,
      tips
    };
  }

  /**
   * 
   * @param { * 消息的通知方式
   * @param nameSub:              二级标题数据
   * @param type:                 类型值：对应接口返回的subType值
   * @param check:                是否选中该方式
   * @param tips:                 提示
   * @param showTips:             是否显示tips
   * @param netField:             接口传递字段
   * @param input:                输入【对应通知方式的联系方式：邮件/手机号】
   * @param needInput:            是否需要输入
   * @param checkInput:           是否勾选输入
   * @param inputTips:            输入的贴士
   * @param inputExplain:         输入的说明
   * @param showNameTips:         是否显示name的tips【用？图标弹出的提示语】
   * @param nameTips:             name的tips【用？图标弹出的提示语】} 
   * 
   * 根据msgItem的值，来初始化msgItem中的notificationWays的值
   */
  initMsgItemNotificationWays(msgItem) {
    let notificationWays = msgItem.notificationWays;
    notificationWays.forEach(wayItem => {
      switch (wayItem.type) {
        case NOTIFICATION_WAYS.RING.type:
          wayItem.check = msgItem.useBell;
          break;
        case NOTIFICATION_WAYS.RECEIVE_CUSTOMER_MSG.type:
          wayItem.check = msgItem.acceptCustomerMsg;
          break;
        case NOTIFICATION_WAYS.MONITOR.type:
          wayItem.check = msgItem.showData;
          break;
        case NOTIFICATION_WAYS.EMIAL.type:
          wayItem.check = msgItem.useEmail;
          wayItem.checkInput = msgItem.useCustomEmail;
          wayItem.input = msgItem.emailList ? msgItem.emailList.join(',') : '';
          break;
        case NOTIFICATION_WAYS.SMS.type:
          wayItem.check = msgItem.usePhone;
          wayItem.checkInput = msgItem.useCustomPhone;
          wayItem.input = msgItem.phoneList ? msgItem.phoneList.join(',') : '';
          break;
        case NOTIFICATION_WAYS.REMINDER_TYPE.type:
          wayItem.noticeType = msgItem.noticeType || [];
          break;
        case NOTIFICATION_WAYS.MPTEMPLATE_MSG.type:
          wayItem.check = msgItem.useMpTemplateMsg;
          break;
      }
    });
  }
  /**
   * 
   * @param { * 消息的通知方式}
   * 
   * 根据msgItem的值，来获取更新通知配置接口的参数
   * msgItem.notificationWays = [
   * {
      type: 2,
      name: '邮件提醒', 
      check: false, 
      tips: '', 
      showTips: false,
      netField: 'emailList',  
      input: '', 
      needInput: true, 
      checkInput: false, 
      inputTips: '自定义邮箱，开启则必填', 
      inputExplain: '消息将会发送至账号绑定的邮箱地址，若需要增加其他邮箱接收，请设置自定义邮箱',
      inputHolder: '多个邮箱，请以，相隔'
    }
   * ]
   */
  getNetUpdateSettingParam(msgItem, aid) {
    let params = {
      aid: aid,
      type: msgItem.type,
      subType: msgItem.subType,
      index: msgItem.index
    };
    let notificationWays = msgItem.notificationWays;
    notificationWays.forEach(wayItem => {
      switch (wayItem.type) {
        case NOTIFICATION_WAYS.RING.type:
          params.useBell = wayItem.check;
          break;
        case NOTIFICATION_WAYS.RECEIVE_CUSTOMER_MSG.type:
          params.acceptCustomerMsg = wayItem.check;
          break;
        case NOTIFICATION_WAYS.MONITOR.type:
          params.showData = wayItem.check;
          break;
        case NOTIFICATION_WAYS.EMIAL.type:
          params.useEmail = wayItem.check;
          params.useCustomEmail = wayItem.checkInput;
          params.emailList = wayItem.checkInput ? wayItem.input.split(",") : [];
          break;
        case NOTIFICATION_WAYS.SMS.type:
          params.usePhone = wayItem.check;
          params.useCustomPhone = wayItem.checkInput;
          params.phoneList = wayItem.checkInput ? wayItem.input.split(",") : [];
          break;
        case NOTIFICATION_WAYS.REMINDER_TYPE.type:
          params.noticeType = this._handlerReminderTypeData(wayItem.noticeType) || [];
          break;
        case NOTIFICATION_WAYS.MPTEMPLATE_MSG.type:
          params.useMpTemplateMsg = wayItem.check;
          break;
      }
    });
    return params;
  }

  // 处理提醒类型的数据
  _handlerReminderTypeData(noticeType) {
    if (!noticeType || !noticeType.length) return [];
    if (noticeType[0] === -1) return [];
    if (noticeType[0] === 0) return [1, 3, 2, 5, 4];
    return noticeType;
  }
}
let messageUtil = new MessageUtil();
export default messageUtil